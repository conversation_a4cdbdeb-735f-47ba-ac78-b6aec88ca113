from typing import Any, AsyncGenerator, List, Tuple

from bots.http.bot_bedrock import http_bedrock_bot_pipeline
from bots.types import BotConfig, BotParams
from common.models import AttachmentModel


async def http_bot_pipeline(
    params: BotParams,
    config: BotConfig,
    messages,
    attachments: List[AttachmentModel],
    language_code: str = "english",
) -> <PERSON><PERSON>[AsyncGenerator[Any, None], Any]:
    """HTTP pipeline that uses AWS Bedrock for LLM services"""
    return await http_bedrock_bot_pipeline(
        params, config, messages, attachments, language_code
    )
